public with sharing class CashFlowConfigController {

    @AuraEnabled(cacheable=true)
    public static String getCashFlowBaseUrl() {
        String orgType;
        // Determine if the org is a sandbox or production using Organization.IsSandbox
        List<Organization> orgs = [SELECT IsSandbox FROM Organization LIMIT 1];
        if (!orgs.isEmpty() && orgs[0].IsSandbox) {
            orgType = 'Sandbox';
        } else {
            orgType = 'Production';
        }

        String settingName = 'CashFlowBaseUrl';
        // Default URL if CMDT record is not found or an error occurs
        String defaultUrl = 'https://mobilizationfunding.com/portal/cash-flow-embed/';

        try {
            List<MF_Config__mdt> configs = [
                SELECT Value__c
                FROM MF_Config__mdt
                WHERE Org_Type__c = :orgType AND Setting_Name__c = :settingName
                LIMIT 1
            ];

            if (!configs.isEmpty() && String.isNotBlank(configs[0].Value__c)) {
                String fetchedUrl = configs[0].Value__c;
                // Ensure the URL ends with a slash
                return fetchedUrl.endsWith('/') ? fetchedUrl : fetchedUrl + '/';
            } else {
                System.debug('CashFlowConfigController: No MF_Config__mdt record found for OrgType: ' + orgType +
                             ' and SettingName: ' + settingName + '. Using default URL.');
                return defaultUrl;
            }
        } catch (Exception e) {
            System.debug('Error fetching CashFlowBaseUrl from Custom Metadata: ' + e.getMessage() +
                         '. Using default URL. StackTrace: ' + e.getStackTraceString());
            return defaultUrl;
        }
    }
}